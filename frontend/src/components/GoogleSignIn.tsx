'use client';

import { useCallback, useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { LiquidButton } from '@/components/ui/liquid-glass-button';

interface GoogleSignInProps {
  returnUrl?: string;
  onButtonClick?: () => void;
  autoTrigger?: boolean;
}

export default function GoogleSignIn({ 
  returnUrl, 
  onButtonClick,
  autoTrigger 
}: GoogleSignInProps) {
  const googleClientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGoogleSignIn = useCallback(
    async () => {
      try {
        setIsLoading(true);
        setError(null);
        const supabase = createClient();

        console.log('Starting Google sign in process');

        const { error } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}/auth/callback${returnUrl ? `?returnUrl=${encodeURIComponent(returnUrl)}` : ''}`,
            scopes: 'email profile',
            queryParams: {
              access_type: 'offline',
              prompt: 'select_account', // Always show account selection
            },
          },
        });

        if (error) throw error;

        // The redirect will be handled by Supabase
        console.log('Google OAuth initiated successfully');
      } catch (error) {
        console.error('Error signing in with Google:', error);
        setError(error instanceof Error ? error.message : 'Failed to sign in with Google');
        setIsLoading(false);
      }
    },
    [returnUrl],
  );

  // Auto-trigger OAuth if template is selected or auto-trigger is enabled
  useEffect(() => {
    if (autoTrigger) {
      handleGoogleSignIn();
    }
  }, [autoTrigger, handleGoogleSignIn]);

  const handleClick = () => {
    if (onButtonClick) {
      // Call custom handler (to show template selector)
      onButtonClick();
    } else {
      // Direct OAuth flow
      handleGoogleSignIn();
    }
  };

  if (!googleClientId) {
    return (
      <button
        disabled
        className="w-full h-12 flex items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full bg-background border border-border opacity-60 cursor-not-allowed"
      >
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            fill="#4285F4"
          />
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            fill="#34A853"
          />
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            fill="#FBBC05"
          />
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            fill="#EA4335"
          />
        </svg>
        Google Sign-In Not Configured
      </button>
    );
  }

  return (
    <>
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 rounded-lg flex items-center gap-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300">
          <svg className="h-5 w-5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
          </svg>
          <span className="text-sm font-medium">{error}</span>
        </div>
      )}

      {/* Liquid Glass Google Sign-In button */}
      <LiquidButton
        onClick={handleClick}
        className="w-full h-16 text-base font-medium relative pr-12"
        size="xxl"
      >
        <div className="flex items-center justify-center gap-4">
          {/* Google logo */}
          <svg className="w-6 h-6" viewBox="0 0 24 24">
            <path
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              fill="#4285F4"
            />
            <path
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              fill="#34A853"
            />
            <path
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              fill="#FBBC05"
            />
            <path
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              fill="#EA4335"
            />
          </svg>

          <span>Login/Sign up with Google</span>
        </div>
      </LiquidButton>
    </>
  );
}