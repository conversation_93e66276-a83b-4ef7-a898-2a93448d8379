FROM ghcr.io/astral-sh/uv:python3.11-alpine

ENV ENV_MODE production
WORKDIR /app

# Install Python dependencies
COPY pyproject.toml uv.lock ./
ENV UV_LINK_MODE=copy
RUN uv sync --locked --quiet

# Copy application code
COPY . .

# Optimized for 8 vCPUs and 16GB RAM with remote Redis/RabbitMQ
# Balanced for high concurrency without context switching overhead
ENV WORKERS=3
ENV THREADS=2
ENV WORKER_CONNECTIONS=100

ENV PYTHONPATH=/app
EXPOSE 8000

# Simplified command for Railway
CMD ["uv", "run", "gunicorn", "api:app", "--workers", "2", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--timeout", "300", "--log-level", "info"]
