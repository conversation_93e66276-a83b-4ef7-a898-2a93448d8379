from typing import Dict

from agent.tools.data_providers.RapidDataProviderBase import RapidDataProviderBase, EndpointSchema


class Twitter<PERSON>rovider(RapidDataProviderBase):
    def __init__(self):
        filters_documentation = """JSON string with advanced filtering options. Available filters:
TIME FILTERS:
- "since": "YYYY-MM-DD" - Start date for tweets
- "until": "YYYY-MM-DD" - End date for tweets

CONTENT FILTERS:
- "lang": "en" - Language (Alpha-2 code)
- "exactSentence": "specific phrase" - Exact phrase match
- "anyOfTheseWords": ["word1", "word2"] - Contains any of these words
- "noneOfTheseWords": ["word1", "word2"] - Excludes these words
- "hashtags": ["tag1", "tag2"] - Contains all hashtags
- "linksInclude": "domain" - Links containing specific word

ACCOUNT FILTERS:
- "fromTheseAccounts": ["user1", "user2"] - Posted by these accounts
- "toTheseAccounts": ["user1"] - Replies to these accounts
- "mentionsTheseAccounts": ["user1"] - Mentions these accounts

ENGAGEMENT FILTERS:
- "minimumLikesCount": 100 - Min likes required
- "minimumRetweetsCount": 50 - Min retweets required
- "minimumRepliesCount": 10 - Min replies required

MEDIA FILTERS:
- "onlyPostsWithMedia": true - Only tweets with media
- "removePostsWithMedia": true - Exclude tweets with media
- "onlyPostsWithPhotos": true - Only tweets with photos
- "onlyPostsWithVideo": true - Only tweets with videos
- "removePostsWithPhotos": true - Exclude photo tweets
- "removePostsWithVideo": true - Exclude video tweets

LINK FILTERS:
- "onlyPostsWithLinks": true - Only tweets with links
- "removePostsWithLinks": true - Exclude tweets with links

REPLY FILTERS:
- "onlyReplies": true - Only show replies
- "removeReplies": true - Exclude replies

LOCATION FILTERS:
- "geocode": "lat,lng" - Geographic coordinates
- "radius": 50 - Radius in kilometers (default: 10)

RETWEET FILTERS:
- "includeRetweets": true - Include retweets in results

COMBINED FILTER EXAMPLES:
Brand monitoring: {"lang": "en", "anyOfTheseWords": ["brand", "product"], "minimumLikesCount": 5, "since": "2024-01-01", "removeReplies": true}

Crisis tracking: {"exactSentence": "breaking news", "minimumRetweetsCount": 25, "onlyPostsWithMedia": true, "since": "2024-01-25"}

Influencer analysis: {"fromTheseAccounts": ["user1", "user2"], "minimumLikesCount": 100, "onlyPostsWithMedia": true, "lang": "en"}

Hashtag campaign: {"hashtags": ["campaign", "event"], "since": "2024-01-01", "until": "2024-01-31", "removeReplies": true, "minimumLikesCount": 10}

Competitor mentions: {"mentionsTheseAccounts": ["competitor"], "noneOfTheseWords": ["spam", "bot"], "minimumLikesCount": 5, "lang": "en"}

Location-based: {"geocode": "40.7128,-74.0060", "radius": 25, "anyOfTheseWords": ["event", "conference"], "onlyPostsWithPhotos": true}

Trending topics: {"since": "2024-01-20", "minimumRetweetsCount": 50, "removeReplies": true, "lang": "en", "onlyPostsWithMedia": true}"""

        endpoints: Dict[str, EndpointSchema] = {
            "search": {
                "route": "/search/{searchTerm}",
                "method": "GET",
                "name": "Twitter AIO Search",
                "description": "Search for tweets using Twitter AIO API with comprehensive filtering options.",
                "payload": {
                    "searchTerm": "Search term (path parameter - required)",
                    "count": "Number of tweets to return (default: 20)",
                    "cursor": "Pagination cursor for next/previous results",
                    "category": "Search category: 'top', 'latest', 'people', 'photos', 'videos'",
                    "includeTimestamp": "Include timestamps in response: true/false",
                    "filters": filters_documentation
                }
            }
        }
        base_url = "https://twitter-aio.p.rapidapi.com"
        super().__init__(base_url, endpoints)


if __name__ == "__main__":
    from dotenv import load_dotenv
    import json
    load_dotenv()
    tool = TwitterProvider()

    # Example 1: Basic search
    basic_search = tool.call_endpoint(
        route="search",
        payload={
            "searchTerm": "elonmusk",
            "count": "20",
            "category": "top"
        }
    )
    print("Basic Search:", basic_search)
    
    # Example 2: Search with date filters
    date_filtered_search = tool.call_endpoint(
        route="search",
        payload={
            "searchTerm": "cybertruck",
            "count": "10",
            "category": "latest",
            "filters": json.dumps({"since": "2020-10-01", "until": "2023-01-20"})
        }
    )
    print("Date Filtered Search:", date_filtered_search)
    
    # Example 3: Advanced filtering with multiple options
    advanced_search = tool.call_endpoint(
        route="search",
        payload={
            "searchTerm": "AI",
            "count": "15",
            "category": "top",
            "includeTimestamp": "true",
            "filters": json.dumps({
                "lang": "en",
                "minimumLikesCount": 100,
                "onlyPostsWithMedia": True,
                "fromTheseAccounts": ["elonmusk", "openai"],
                "hashtags": ["AI", "MachineLearning"]
            })
        }
    )
    print("Advanced Search:", advanced_search)