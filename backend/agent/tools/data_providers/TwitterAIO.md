Details from docs of Twitter AIO 11api: 

Query Params: 
- count 
- cursor (At the end of the entries array you'll find two objects with the type TimelineTimelineCursor. There is one cursor to go up (Top) and one for going down (bottom) the list. You just need to provide the value as the cursor parameter to get the next page of the pagination.)

- category (top, latest, people, photos, videos)

- includeTimestamp: false/true

- filters (stored as Json objects; more detail below)

for eg. 
filter = {"since":"2020-10-01", "until":"2023-01-20"}

Search filters have also been added. A stringified JSON containing an object and with the following options can be sent via ?filters. The filters work the same way as in the Twitter/X mobile or web app.

lang (string)
This allows tweets to be sorted by language. An Alpha-2 country code must be provided for this. country codes
Example:

{
  "lang": "en"
}
since (string)
"since" can be used to set a start date for a date range. Without "until", the date range applies from "since" until now. The format "YYYY-MM-DD" must be transmitted.
Example:

{
  "since": "2020-10-31"
}
until (string)
"until" can be used to set a end date for a date range. Without "since", the date range applies from first tweeets until "until". The format "YYYY-MM-DD" must be transmitted.
Example:

{
  "until": "2023-01-20"
}
exactSentence (string)
"exactSentence" can be used to search for a complete sentence. The filter requires that the sentence is contained in the tweet.
Example:

{
  "exactSentence": "And the winner is"
}
anyOfTheseWords (string array)
This will display every tweet that contains one of the words. An array of strings must be passed.
Example:

{
  "anyOfTheseWords": [
    "winner",
    "champion"
  ]
}
noneOfTheseWords (string array)
This will display every tweet that does not contain these words. An array of strings must be passed.
Example:

{
  "noneOfTheseWords": [
    "winner",
    "spoiler"
  ]
}
hashtags (string array)
This will display every tweet that contain all of the hashtags. An array of strings must be passed.
Example:

{
  "hashtags": [
    "Oscars",
    "AcademyAwards"
  ]
}
fromTheseAccounts (string array)
This will display every tweet that is posted by one of the accounts. An array of strings must be passed.
Example:

{
  "fromTheseAccounts": [
    "elonmusk",
    "X"
  ]
}
toTheseAccounts (string array)
This will display every tweet/reply that is posted as an answer to one of the accounts. An array of strings must be passed.
Example:

{
  "toTheseAccounts": [
    "elonmusk"
  ]
}
mentionsTheseAccounts (string array)
This will display every tweet that mention one of the accounts. An array of strings must be passed.
Example:

{
  "mentionsTheseAccounts": [
    "jimmykimmel"
  ]
}
removePostsWithLinks (boolean)
This will not show tweets which include a link.
Example:

{
  "removePostsWithLinks": true
}
onlyPostsWithLinks (boolean)
This will only show tweets which include a link.
Example:

{
  "onlyPostsWithLinks": true
}
removeReplies (boolean)
This will exclude replies from the search results.
Example:

{
  "removeReplies": true
}
onlyReplies (boolean)
This will only show replies in the search results.
Example:

{
  "onlyReplies": true
}
removePostsWithMedia (boolean)
This will not show tweets which include any type of media.
Example:

{
  "removePostsWithMedia": true
}
onlyPostsWithMedia (boolean)
This will only show tweets which include any type of media.
Example:

{
  "onlyPostsWithMedia": true
}
removePostsWithVideo (boolean)
This will not show tweets which include a video.
Example:

{
  "removePostsWithVideo": true
}
onlyPostsWithVideo (boolean)
This will only show tweets which include a video.
Example:

{
  "onlyPostsWithVideo": true
}
removePostsWithPhotos (boolean)
This will not show tweets which include photos.
Example:

{
  "removePostsWithPhotos": true
}
onlyPostsWithPhotos (boolean)
This will only show tweets which include photos.
Example:

{
  "onlyPostsWithPhotos": true
}
linksInclude (string)
This will filter tweets by checking if the link contains a word.
Example:

{
  "linksInclude": "oscars"
}
minimumRepliesCount (integer)
This means that only tweets with at least this number of replies can be displayed.
Example:

{
  "minimumRepliesCount": 10
}
minimumRetweetsCount (integer)
This means that only tweets with at least this number of retweets can be displayed.
Example:

{
  "minimumRetweetsCount": 100
}
minimumLikesCount (integer)
This means that only tweets with at least this number of likes can be displayed.
Example:

{
  "minimumLikesCount": 520
}
includeRetweets
With this filter you will also get retweets matching your search term and other filters

{
  "includeRetweets": true
}
geocode (lat,lng as string)
Tweets can be restricted to a specific location using the geocode. This requires the geocode from latitude and longitude. You can get these values from Google Maps or geocode.xyz.
Example:

{
  "geocode": "40.76997,-73.97225"
}
radius (integer)
The geocode also assumes a radius. This is specified in kilometers and is 10km by default.
Example:

{
  "radius": 100
}

Path Params: 
searchTerm


EXAMPLE CURL REQUEST: 

curl --request GET 
	--url 'https://twitter-aio.p.rapidapi.com/search/elonmusk?count=20&category=Top&filters=%7B%22since%22%3A%20%222020-10-01%22%7D&includeTimestamp=false' 
	--header 'x-rapidapi-host: twitter-aio.p.rapidapi.com' 
	--header 'x-rapidapi-key: **************************************************'

Here count = 20, cursor = Top, searchTerm = elonmusk, filters (just "since" is used here but additional could be used.)
