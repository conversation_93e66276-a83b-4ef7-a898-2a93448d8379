from firecrawl import <PERSON><PERSON>raw<PERSON>A<PERSON>
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema
from utils.config import config
from agentpress.thread_manager import ThreadManager
import json
import logging
from typing import Optional, List, Dict, Any
import datetime
import asyncio

class FirecrawlTool(Tool):
    """Tool for deep web research using Firecrawl's advanced extract capabilities with LLM-powered data extraction."""

    def __init__(self, **kwargs):
        super().__init__()
        # Accept any kwargs for compatibility but don't require specific ones
        self.project_id = kwargs.get('project_id')
        self.thread_manager = kwargs.get('thread_manager')
        # Load environment variables
        load_dotenv()
        # Use API key from config
        self.firecrawl_api_key = config.FIRECRAWL_API_KEY
        
        if not self.firecrawl_api_key:
            raise ValueError("FIRECRAWL_API_KEY not found in configuration")

        # Initialize Firecrawl client
        self.firecrawl_client = FirecrawlApp(api_key=self.firecrawl_api_key)
    
    def fail_response(self, message: str) -> ToolResult:
        """Return a failure response."""
        return ToolResult(success=False, output=message)

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "extract_web_data",
            "description": "Extract structured data from multiple web pages using AI. This tool is designed for comprehensive web research, data collection, and deep analysis across multiple URLs. It can extract specific information based on your prompt, follow links for broader coverage, and return structured data. ALWAYS use this tool AFTER web_search to extract detailed information from the URLs found. The tool excels at: extracting company information, product details, pricing data, contact information, technical specifications, news and announcements, and any structured data from web pages.",
            "parameters": {
                "type": "object",
                "properties": {
                    "urls": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "description": "List of URLs to extract data from. You can include specific pages or use wildcards (e.g., 'https://example.com/*' to crawl entire domains). For comprehensive research, include multiple relevant URLs from your web search results."
                    },
                    "prompt": {
                        "type": "string",
                        "description": "Natural language prompt describing what data to extract. Be specific and detailed. Examples: 'Extract all product features, pricing tiers, and customer testimonials', 'Find and extract all contact information including emails, phone numbers, and office addresses', 'Extract technical specifications, API documentation, and integration guides'."
                    },
                    "schema": {
                        "type": "string",
                        "description": "Optional JSON schema as a string to enforce structure on extracted data. Use this when you need data in a specific format. If not provided, the AI will choose an appropriate structure based on your prompt."
                    },
                    "enableWebSearch": {
                        "type": "boolean",
                        "description": "If true, the extraction will follow links and search for related information beyond the provided URLs. Use this for more comprehensive research when you want to discover additional relevant data.",
                        "default": False
                    },
                    "wait_for_completion": {
                        "type": "boolean",
                        "description": "If true, wait for the extraction to complete and return results. If false, return immediately with a job ID that can be checked later. Use false for large-scale extractions that might take time.",
                        "default": True
                    }
                },
                "required": ["urls", "prompt"]
            }
        }
    })
    @xml_schema(
        tag_name="extract-web-data",
        mappings=[
            {"param_name": "urls", "node_type": "element", "path": "urls/url"},
            {"param_name": "prompt", "node_type": "element", "path": "prompt"},
            {"param_name": "schema", "node_type": "element", "path": "schema"},
            {"param_name": "enableWebSearch", "node_type": "attribute", "path": "."},
            {"param_name": "wait_for_completion", "node_type": "attribute", "path": "."}
        ],
        example='''
        <function_calls>
        <invoke name="extract_web_data">
        <parameter name="urls">["https://example.com/products", "https://example.com/pricing", "https://example.com/about"]</parameter>
        <parameter name="prompt">Extract all product features, pricing information, company details, and customer testimonials from these pages</parameter>
        <parameter name="enableWebSearch">true</parameter>
        </invoke>
        </function_calls>
        
        <!-- Another example with schema -->
        <function_calls>
        <invoke name="extract_web_data">
        <parameter name="urls">["https://techcompany.com/*"]</parameter>
        <parameter name="prompt">Extract all technical documentation, API endpoints, and integration guides</parameter>
        <parameter name="schema">{"api_endpoints": [{"endpoint": "string", "method": "string", "description": "string"}], "integration_guides": [{"title": "string", "url": "string", "summary": "string"}]}</parameter>
        </invoke>
        </function_calls>
        '''
    )
    async def extract_web_data(
        self, 
        urls: List[str],
        prompt: str,
        schema: Optional[str] = None,
        enableWebSearch: bool = False,
        wait_for_completion: bool = True,
        **kwargs  # Accept additional parameters for compatibility
    ) -> ToolResult:
        """
        Extract structured data from web pages using AI-powered analysis.
        
        This tool excels at:
        - Comprehensive data extraction from multiple sources
        - Following links and discovering related information
        - Extracting specific data points based on natural language prompts
        - Structuring data according to custom schemas
        - Deep research across entire domains
        """
        try:
            # Handle parameter variations from frontend for backward compatibility
            # Support legacy parameter names
            if 'web_search' in kwargs and enableWebSearch is False:
                enableWebSearch = kwargs.get('web_search', False)
            if 'enable_web_search' in kwargs and enableWebSearch is False:
                enableWebSearch = kwargs.get('enable_web_search', False)
            if 'extraction_prompt' in kwargs and not prompt:
                prompt = kwargs.get('extraction_prompt', prompt)
            if 'schema_json' in kwargs and not schema:
                schema = kwargs.get('schema_json', schema)
            
            # Validate inputs
            if not urls or not isinstance(urls, list):
                return self.fail_response("Valid list of URLs is required.")
            
            if not prompt or not isinstance(prompt, str):
                return self.fail_response("A valid prompt is required.")
            
            # Parse schema if provided
            parsed_schema = None
            if schema:
                try:
                    if isinstance(schema, str):
                        parsed_schema = json.loads(schema)
                    else:
                        parsed_schema = schema
                    logging.info(f"Using custom schema for extraction: {parsed_schema}")
                except (json.JSONDecodeError, TypeError) as e:
                    logging.warning(f"Invalid JSON schema provided: {e}")
                    return self.fail_response(f"Invalid JSON schema: {str(e)}")
            
            # Log extraction request
            logging.info(f"Starting Firecrawl extraction for {len(urls)} URLs")
            logging.info(f"Prompt: {prompt}")
            logging.info(f"Enable web search: {enableWebSearch}")
            
            # Prepare extraction parameters according to SDK
            extract_params = {}
            
            # Add prompt
            extract_params["prompt"] = prompt
            
            # Add enableWebSearch if True (SDK parameter name)
            if enableWebSearch:
                extract_params["enableWebSearch"] = enableWebSearch
            
            # Add schema if provided
            if parsed_schema:
                extract_params["schema"] = parsed_schema
            
            # Execute extraction
            if wait_for_completion:
                # Synchronous extraction - wait for results
                logging.info("Executing synchronous extraction (waiting for completion)")
                
                # Run the blocking extract call in a thread pool to avoid blocking the event loop
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    lambda: self.firecrawl_client.extract(urls, **extract_params)
                )
                
                # Process results
                if result.success:
                    logging.info(f"Extraction completed successfully")
                    
                    # Save results to a file
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"firecrawl_extract_{timestamp}.json"
                    
                    # Prepare the data to save
                    save_data = {
                        "prompt": prompt,
                        "urls": urls,
                        "enableWebSearch": enableWebSearch,
                        "timestamp": timestamp,
                        "data": result.data
                    }
                    
                    # Format the results
                    json_content = json.dumps(save_data, ensure_ascii=False, indent=2)
                    
                    # Prepare success message with the extracted data
                    message = f"Successfully extracted data from {len(urls)} URLs.\n\n"
                    message += "Extracted Data:\n"
                    message += json.dumps(result.data, ensure_ascii=False, indent=2)
                    
                    return ToolResult(
                        success=True,
                        output=message
                    )
                else:
                    error_msg = getattr(result, 'error', 'Unknown error during extraction')
                    logging.error(f"Extraction failed: {error_msg}")
                    return self.fail_response(f"Extraction failed: {error_msg}")
                    
            else:
                # Asynchronous extraction - return job ID
                logging.info("Executing asynchronous extraction (returning job ID)")
                
                # Run the async extract call
                loop = asyncio.get_event_loop()
                job = await loop.run_in_executor(
                    None,
                    lambda: self.firecrawl_client.async_extract(urls, **extract_params)
                )
                
                if job and hasattr(job, 'id'):
                    message = f"Extraction job started successfully.\n"
                    message += f"Job ID: {job.id}\n"
                    message += f"Use the 'check_extract_status' function with this ID to check progress."
                    
                    return ToolResult(
                        success=True,
                        output=message
                    )
                else:
                    return self.fail_response("Failed to start extraction job")
        
        except Exception as e:
            error_message = str(e)
            logging.error(f"Error in extract_web_data: {error_message}")
            return self.fail_response(f"Error during extraction: {error_message[:500]}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "check_extract_status",
            "description": "Check the status of an asynchronous Firecrawl extraction job. Use this to monitor progress and retrieve results of extraction jobs started with wait_for_completion=false.",
            "parameters": {
                "type": "object",
                "properties": {
                    "job_id": {
                        "type": "string",
                        "description": "The job ID returned from a previous extract_web_data call with wait_for_completion=false"
                    }
                },
                "required": ["job_id"]
            }
        }
    })
    @xml_schema(
        tag_name="check-extract-status",
        mappings=[
            {"param_name": "job_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <function_calls>
        <invoke name="check_extract_status">
        <parameter name="job_id">abc123-def456-ghi789</parameter>
        </invoke>
        </function_calls>
        '''
    )
    async def check_extract_status(
        self,
        job_id: str
    ) -> ToolResult:
        """
        Check the status of an asynchronous extraction job.
        """
        try:
            if not job_id:
                return self.fail_response("Job ID is required")
            
            logging.info(f"Checking status for extraction job: {job_id}")
            
            # Check job status
            loop = asyncio.get_event_loop()
            status = await loop.run_in_executor(
                None,
                lambda: self.firecrawl_client.get_extract_status(job_id)
            )
            
            if status:
                message = f"Extraction Job Status:\n"
                message += f"Job ID: {job_id}\n"
                message += f"Status: {status.status}\n"
                
                if hasattr(status, 'expires_at'):
                    message += f"Expires at: {status.expires_at}\n"
                
                if status.status == 'completed' and status.success and status.data:
                    # Save completed results
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"firecrawl_extract_completed_{timestamp}.json"
                    
                    save_data = {
                        "job_id": job_id,
                        "status": "completed",
                        "timestamp": timestamp,
                        "data": status.data
                    }
                    
                    # Format the results
                    json_content = json.dumps(save_data, ensure_ascii=False, indent=2)
                    
                    message += f"\nExtraction completed successfully!\n\n"
                    message += "Extracted Data:\n"
                    message += json.dumps(status.data, ensure_ascii=False, indent=2)
                    
                elif status.status == 'processing':
                    message += "\nExtraction is still in progress. Check again later."
                    
                elif status.status == 'failed':
                    error_msg = getattr(status, 'error', 'Unknown error')
                    message += f"\nExtraction failed: {error_msg}"
                    
                return ToolResult(
                    success=True,
                    output=message
                )
            else:
                return self.fail_response(f"Could not retrieve status for job ID: {job_id}")
                
        except Exception as e:
            error_message = str(e)
            logging.error(f"Error checking extract status: {error_message}")
            return self.fail_response(f"Error checking job status: {error_message[:500]}")

if __name__ == "__main__":
    async def test_extract():
        """Test function for the Firecrawl extract tool"""
        print("Test function needs to be updated for sandbox version")
    
    async def run_tests():
        """Run all test functions"""
        await test_extract()
        
    asyncio.run(run_tests())