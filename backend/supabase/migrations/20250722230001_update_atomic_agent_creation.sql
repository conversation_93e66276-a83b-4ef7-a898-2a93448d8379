-- Update the atomic agent creation function to remove agent_version_history dependency
CREATE OR REPLACE FUNCTION create_agent_with_version(
    p_account_id UUID,
    p_name VARCHAR(255),
    p_description TEXT,
    p_system_prompt TEXT,
    p_configured_mcps JSONB DEFAULT '[]'::jsonb,
    p_custom_mcps JSONB DEFAULT '[]'::jsonb,
    p_agentpress_tools JSONB DEFAULT '{}'::jsonb,
    p_is_default BOOLEAN DEFAULT false,
    p_avatar VARCHAR(10) DEFAULT NULL,
    p_avatar_color VARCHAR(7) DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
DECLARE
    v_agent_record RECORD;
    v_version_record RECORD;
    v_result JSONB;
BEGIN
    -- Input validation
    IF p_account_id IS NULL THEN
        RAISE EXCEPTION 'account_id cannot be null';
    END IF;
    
    IF p_name IS NULL OR trim(p_name) = '' THEN
        RAISE EXCEPTION 'name cannot be null or empty';
    END IF;
    
    IF p_system_prompt IS NULL OR trim(p_system_prompt) = '' THEN
        RAISE EXCEPTION 'system_prompt cannot be null or empty';
    END IF;

    -- If this is set as default, unset other defaults first
    IF p_is_default = true THEN
        UPDATE agents 
        SET is_default = false 
        WHERE account_id = p_account_id AND is_default = true;
    END IF;

    -- Step 1: Create the agent
    INSERT INTO agents (
        account_id,
        name,
        description,
        system_prompt,
        configured_mcps,
        custom_mcps,
        agentpress_tools,
        is_default,
        avatar,
        avatar_color,
        version_count
    ) VALUES (
        p_account_id,
        p_name,
        p_description,
        p_system_prompt,
        p_configured_mcps,
        p_custom_mcps,
        p_agentpress_tools,
        p_is_default,
        p_avatar,
        p_avatar_color,
        1
    ) RETURNING * INTO v_agent_record;

    -- Step 2: Create v1 version automatically
    INSERT INTO agent_versions (
        agent_id,
        version_number,
        version_name,
        system_prompt,
        configured_mcps,
        custom_mcps,
        agentpress_tools,
        is_active,
        created_by
    ) VALUES (
        v_agent_record.agent_id,
        1,
        'v1',
        p_system_prompt,
        p_configured_mcps,
        p_custom_mcps,
        p_agentpress_tools,
        true,
        p_account_id
    ) RETURNING * INTO v_version_record;

    -- Step 3: Update agent with current version
    UPDATE agents 
    SET current_version_id = v_version_record.version_id
    WHERE agent_id = v_agent_record.agent_id;

    -- Build the complete response
    v_result := jsonb_build_object(
        'agent_id', v_agent_record.agent_id,
        'account_id', v_agent_record.account_id,
        'name', v_agent_record.name,
        'description', v_agent_record.description,
        'system_prompt', v_agent_record.system_prompt,
        'configured_mcps', v_agent_record.configured_mcps,
        'custom_mcps', v_agent_record.custom_mcps,
        'agentpress_tools', v_agent_record.agentpress_tools,
        'is_default', v_agent_record.is_default,
        'is_public', v_agent_record.is_public,
        'marketplace_published_at', v_agent_record.marketplace_published_at,
        'download_count', v_agent_record.download_count,
        'tags', v_agent_record.tags,
        'avatar', v_agent_record.avatar,
        'avatar_color', v_agent_record.avatar_color,
        'created_at', v_agent_record.created_at,
        'updated_at', v_agent_record.updated_at,
        'current_version_id', v_version_record.version_id,
        'version_count', v_agent_record.version_count,
        'current_version', jsonb_build_object(
            'version_id', v_version_record.version_id,
            'agent_id', v_version_record.agent_id,
            'version_number', v_version_record.version_number,
            'version_name', v_version_record.version_name,
            'system_prompt', v_version_record.system_prompt,
            'configured_mcps', v_version_record.configured_mcps,
            'custom_mcps', v_version_record.custom_mcps,
            'agentpress_tools', v_version_record.agentpress_tools,
            'is_active', v_version_record.is_active,
            'created_at', v_version_record.created_at,
            'updated_at', v_version_record.updated_at,
            'created_by', v_version_record.created_by
        )
    );

    RETURN v_result;

EXCEPTION
    WHEN OTHERS THEN
        -- Log the error details
        RAISE EXCEPTION 'Agent creation transaction failed: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
END;
$$;

-- Update comment
COMMENT ON FUNCTION create_agent_with_version IS 'Atomically creates an agent with its initial version v1. All operations succeed or fail together. Version history is not tracked in this function.';